Please refer to the below pre-clinical Antibody-Drug Conjugate (ADC) study and extract ALL the experimental models used to study the ADC provided for a specific experiment type.
You must list down every experimental model that this ADC is evaluated in for {{EXPERIMENT_TYPE}}.

Specific Antibody-Drug Conjugate (ADC) to focus: {{ADC}}
Specific Experiment Type: {{EXPERIMENT_TYPE}}

Please follow the below steps:

Step-1:
YOU HAVE TO LOAD ALL MODEL NAMES INTO MEMORY!!

Please extract names of ALL experimental models used to study the ADC and experiment provided above, (take special care in ensuring that every single experimental model is picked only for the specified experiment type mentioned in the context) and save them into memory along with the step-by-step thought process quoting text snippets from the research paper supporting your arguments for being exhaustive in your extraction. Even extract the models that are used for safety and pharmacokinetics studies, do not stay limited to cancer related studies only.

CRITICAL REQUIREMENT: Make sure that you pick EVERY SINGLE experimental model that this ADC is evaluated in for the given experiment type. Do not miss any model, even if it is mentioned only once in the text. Strictly check if the model belongs under the experiment type provided.

CRITICAL REQUIREMENT:  
For each experimental model, you MUST assign the correct model_type based on BOTH the model name and the experiment type.  
For example, the same model name (such as "RH-41") may have different model types depending on the experiment type:
- If experiment type is "In Vitro", model_type should be "Cell Line Model"
- If experiment type is "In Vivo", model_type should be "Cell Line-Derived Xenograft (CDX)" or another appropriate in vivo type
- If experiment type is "Ex Vivo", model_type should be "Tissue Specimens" or another appropriate ex vivo type
- For a model mentioned in context is derived from a cell line do not capture it as a cell line model in In Vitro unless this cell line is explicitly used in an in vitro experiment.
Do NOT assign the same model_type to the same model name across different experiment types unless the context in the text supports it.
Always use the experiment type context to determine the correct model_type.

After this please do the following:

Step-2:
For the ADC provided, extract all experimental models used with supporting evidence as citations. 

Instructions:

For each experimental model that the specified ADC (Antibody Drug Conjugate) is studied in, extract the following:

1. citations:
   - List of sentences or fragments from the research paper that support the extraction of the below attributes for this model.
   - Each citation sentence should provide clear evidence for one or more of the below attributes.
   - Include enough context (typically 20-30 words long per sentence) to validate the extraction for every field in this model.
   - Ensure the citation includes relevant descriptors that support the classification.
   - The citations must contain the name of the ADC and the experimental model on which this ADC has been evaluated. This must provide sufficient context to link this experimental model to the ADC.
   - The citations must be EXACT excerpts from the research paper, you can add many excerpts but can't modify any!

2. model_name:
   - The name of the experimental model for the given ADC.

3. model_type:
   - The type of the experimental model for the given ADC.
   - It is one of the following only:
      CELL_LINE = "Cell Line Model"
      CDX = "Cell Line-Derived Xenograft (CDX)"
      PDX = "Patient-Derived Xenograft (PDX)"
      ORGANOID = "Organoid Model"
      SYNGENEIC = "Syngeneic Model"
      TISSUE_SPECIMENS = "Tissue Specimens"
      TRANSGENIC = "Transgenic Model"
      NON_CELL_BASED = "Non-cell based Model"
      RODENT_MODELS = "Rodent Models"
      NON_HUMAN_PRIMATES = "Non-Human Primate Models"
      NONE = "NONE"

4. cancer_type:
   - A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.

5. cancer_subtype (if specified):
   - A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.

Rules:
- Do not duplicate the same model; each model should appear only once in your output, even if mentioned multiple times. For this: Take special care in picking a model only once even if its constituent is mentioned along with the model, only the model will be picked and not the constituent.
- For each model, include all relevant supporting citations in the citations list.
- Do not miss any models.

CRITICAL REQUIREMENT: In this step, only extract experimental models that our ADC is evaluated in. And extract ALL such models, do not stay limited to cancer related models only. Do not stay limited to any kind of model or study, extract all models mentioned in relation to the ADC provided.

To help you, we have provided a rough extraction of all experimental models that are mentioned in this text here (These are not necessarily separated into a list of unique models yet but the below text contains many models that you MUST consider):

{{ALL_MODELS}}

Extract all models for below experiment type for the ADC provided:
{{EXPERIMENT}}

Text to analyze:
{{TEXT}}


Return all models used with this ADC, ensuring each model appears only once in your response and every unique model must be mentioned distinctly as a different model in your response and ensure you pick ALL the models related to this ADC as per the text provided. Do not club multiple models together, each unique model with its details details must be returned as a separate model in your response.