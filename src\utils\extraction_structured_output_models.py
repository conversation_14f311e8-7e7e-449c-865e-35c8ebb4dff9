import sys
import json
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field
from enum import Enum


# ---- Definition of Antibody Drug Conjugate (ADC) Components ----

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"


class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"


class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"


class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"


class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )

print("="*100)
print(json.dumps(AntibodyDrugConjugate.model_json_schema(), indent=2))
print("="*100)

# ---- Definition of Preclinical Experimental Models ----


class ModelType(str, Enum):
    """
    Hierarchical enumeration of experimental model types used in preclinical ADC research.

    This classification organizes experimental models into logical categories based on their
    biological complexity, clinical relevance, and experimental applications. Models progress
    from simple in vitro systems to complex in vivo models that better recapitulate human
    disease and therapeutic responses.

    HIERARCHICAL STRUCTURE:

    1. IN VITRO MODELS (Laboratory-based systems)
       ├── CELL_LINE: Immortalized cancer cell cultures
       ├── NON_CELL_BASED: Biochemical and protein-based assays

    2. EX VIVO MODELS (Tissue-based systems outside the body)
       ├── ORGANOID: 3D tissue cultures preserving architecture
       ├── TISSUE_SPECIMENS: Fresh/preserved human tissue samples

    3. IN VIVO MODELS (Whole organism studies)
       ├── Xenograft Models (Human cancer in immunocompromised hosts)
       │   ├── CDX: Cell line-derived xenografts
       │   └── PDX: Patient-derived xenografts
       ├── Syngeneic Models (Mouse cancer in immunocompetent hosts)
       │   └── SYNGENEIC: Matched mouse cancer cell lines
       ├── Transgenic Models (Genetically engineered cancer development)
       │   └── TRANSGENIC: Spontaneous cancer in modified mice
       └── Specialized Models
           ├── RODENT_MODELS: General mouse/rat cancer models
           └── NON_HUMAN_PRIMATES: Primate safety/efficacy studies

    DETAILED MODEL DEFINITIONS:

    CELL_LINE: Immortalized cancer cell lines maintained in laboratory culture conditions.
    Setup: Cancer cells grown in controlled media with defined nutrients and growth factors.
    Key Features: Unlimited proliferation, genetic stability over short passages, standardized conditions.
    Distinguishing Factors: Pure cancer cell population, no stromal components, 2D growth pattern.

    NON_CELL_BASED: Biochemical assays, purified protein studies, and computational models.
    Setup: Cell-free systems using purified proteins, enzymes, or mathematical modeling.
    Key Features: Mechanistic insights into drug-target interactions, high-throughput screening capability.
    Distinguishing Factors: No living cells involved, direct molecular interaction studies.

    ORGANOID: Three-dimensional tissue cultures derived from patient or cell line material.
    Setup: Cells grown in specialized matrices (Matrigel) with organ-specific media conditions.
    Key Features: Preserves tissue architecture, cell-cell interactions, some organ functionality.
    Distinguishing Factors: 3D structure, multiple cell types, partial recapitulation of organ function.

    TISSUE_SPECIMENS: Fresh or preserved human tissue samples for ex vivo drug testing.
    Setup: Primary human tumor tissue maintained in culture for short-term drug exposure studies.
    Key Features: Native tumor architecture, original cellular composition, patient-specific responses.
    Distinguishing Factors: Direct patient material, limited viability window, heterogeneous composition.

    CDX (Cell Line-Derived Xenograft): Established human cancer cell lines implanted in immunodeficient mice.
    Setup: Human cancer cell lines injected subcutaneously or orthotopically into nude/SCID mice.
    Key Features: Reproducible tumor growth, well-characterized genetics, cost-effective.
    Distinguishing Factors: Uses established cell lines (not primary tissue), immunocompromised host required.

    PDX (Patient-Derived Xenograft): Primary patient tumor tissue directly implanted in immunodeficient mice.
    Setup: Fresh patient tumor fragments implanted directly into immunodeficient mice without culture.
    Key Features: Preserves original tumor heterogeneity, maintains stromal components, clinically relevant.
    Distinguishing Factors: Direct patient tissue transfer, preserves tumor microenvironment, no cell culture step.

    SYNGENEIC: Mouse cancer cell lines grown in immunocompetent mice of the same genetic background.
    Setup: Mouse-derived cancer cells injected into genetically matched immunocompetent mice.
    Key Features: Intact immune system, immune-oncology studies possible, species-matched genetics.
    Distinguishing Factors: Immunocompetent host, mouse-specific biology, enables immune system studies.

    TRANSGENIC: Genetically modified mice that spontaneously develop cancer through engineered mutations.
    Setup: Mice with engineered oncogenes/tumor suppressors that develop cancer naturally over time.
    Key Features: Natural tumor development, intact immune system, controlled genetic background.
    Distinguishing Factors: Spontaneous cancer development, no transplantation required, genetic control.

    RODENT_MODELS: General category for mouse and rat cancer models not fitting other specific categories.
    Setup: Various approaches including carcinogen-induced tumors, spontaneous tumors, or unspecified methods.
    Key Features: Broad category encompassing multiple rodent-based approaches, regulatory acceptance.
    Distinguishing Factors: General classification when specific model type unclear, includes non-transplant models.

    NON_HUMAN_PRIMATES: Primate models used for safety, toxicology, and efficacy studies.
    Setup: Cancer studies or safety evaluations in monkeys, apes, or other non-human primates.
    Key Features: Closest phylogenetic relationship to humans, regulatory requirement for some studies.
    Distinguishing Factors: Primate physiology, ethical considerations, highest clinical relevance for safety.
    """

    # === IN VITRO MODELS ===
    # Laboratory-based experimental systems
    CELL_LINE = "Cell Line Model"
    NON_CELL_BASED = "Non-cell based Model"

    # === EX VIVO MODELS ===
    # Tissue-based systems outside the body
    ORGANOID = "Organoid Model"
    TISSUE_SPECIMENS = "Tissue Specimens"

    # === IN VIVO XENOGRAFT MODELS ===
    # Human cancer cells grown in immunocompromised animal hosts
    CDX = "Cell Line-Derived Xenograft (CDX)"
    PDX = "Patient-Derived Xenograft (PDX)"

    # === IN VIVO SYNGENEIC MODELS ===
    # Mouse cancer cells in immunocompetent mouse hosts
    SYNGENEIC = "Syngeneic Model"

    # === IN VIVO TRANSGENIC MODELS ===
    # Genetically engineered mouse models with spontaneous cancer development
    TRANSGENIC = "Transgenic Model"

    # === SPECIALIZED IN VIVO MODELS ===
    # Broader categories for specific research applications
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primate Models"

    # === UNDEFINED ===
    # Used when model type information is not available or not applicable
    NONE = "NONE"


class PreclinicalExperimentalModel(BaseModel):
    """
    Comprehensive model for preclinical experimental systems used in ADC research and testing.

    This model captures detailed information about the experimental platforms used to evaluate
    Antibody Drug Conjugates (ADCs) in preclinical studies. It encompasses the full spectrum
    of model systems from simple cell culture to complex animal models, providing context
    for interpreting experimental results and translating findings to clinical applications.

    The model includes information about:
    - Model system characteristics and classification
    - Cancer type and subtype being studied
    - Supporting citations from research literature

    This information is critical for understanding the relevance and limitations of
    preclinical ADC efficacy and safety data.
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper describing the experimental model system used",
    )

    # Model identification and classification
    model_name: str = Field(
        ...,
        description="Specific name or identifier of the experimental model system used in the ADC study",
    )

    model_type: ModelType = Field(
        ...,
        description="Classification of the experimental model system based on biological complexity and experimental approach",
    )

    # Cancer classification fields
    cancer_type: str = Field(
        ...,
        description="Primary cancer classification based on the organ or tissue of origin where the malignancy first developed",
    )

    cancer_subtype: Optional[str] = Field(
        None,
        description="Detailed cancer classification based on histological, molecular, or genetic characteristics that influence treatment response and prognosis",
    )
