import sys
import json
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum


# ---- Definition of Antibody Drug Conjugate (ADC) Components ----

class LinkerType(str, Enum):
    """
    Enumeration of linker types used in Antibody Drug Conjugates (ADCs).

    Linkers are chemical compounds that connect the antibody to the cytotoxic payload.
    The type of linker affects drug release mechanism and therapeutic efficacy.
    """
    # Linkers that can be cleaved inside target cells or sometimes in tumour microenvironment to release the payload
    CLEAVABLE = "Cleavable Linker"

    # Linkers that remain attached to the payload after internalization
    NON_CLEAVABLE = "Non-cleavable Linker"

    # Used when linker information is not available or not applicable
    NONE = "NONE"


class AntibodyClonality(str, Enum):
    """
    Enumeration of antibody clonality types.

    Clonality refers to whether the antibody is derived from a single clone
    or multiple clones of B cells, affecting specificity and production.
    """
    # Antibodies derived from a single clone of B cells, providing high specificity
    MONOCLONAL = "Monoclonal Antibody (mAb)"

    # Antibodies derived from multiple clones of B cells, providing broader reactivity
    POLYCLONAL = "Polyclonal Antibody (pAb)"

    # Used when clonality information is not available or not applicable
    NONE = "NONE"


class AntibodySpecies(str, Enum):
    """
    Enumeration of antibody species origin and engineering types.

    This classification indicates the source organism and level of humanization,
    which affects immunogenicity and therapeutic potential in humans.
    """
    # Antibodies derived from mice, may cause immunogenic reactions in humans
    MURINE = "Murine"

    # Antibodies with mouse variable regions and human constant regions
    CHIMERIC = "Chimeric"

    # Antibodies with human framework regions and mouse complementarity-determining regions
    HUMANIZED = "Humanized"

    # Used when species information is not available or not applicable
    NONE = "NONE"


class AntibodyIsotype(str, Enum):
    """
    Enumeration of antibody isotype classifications.

    Isotypes are classes of antibodies defined by their heavy chain constant regions,
    each with distinct biological functions and properties.
    """
    # Immunoglobulin G - most common therapeutic antibody class
    IGG = "IgG"

    # Immunoglobulin M - pentameric antibody, first responder in immune response
    IGM = "IgM"

    # Immunoglobulin A - primarily found in mucosal areas and secretions
    IGA = "IgA"

    # Immunoglobulin E - involved in allergic reactions and parasitic infections
    IGE = "IgE"

    # Immunoglobulin D - functions as antigen receptor on B cells
    IGD = "IgD"

    # Used when isotype information is not available or not applicable
    NONE = "NONE"


class AntibodyDrugConjugateType(str, Enum):
    """
    Enumeration of ADC types based on their role in research studies.

    This classification distinguishes between ADCs being tested versus
    those used as reference standards or comparators.
    """
    # ADCs that are the primary subject of investigation in the study
    INVESTIGATIVE = "Investigative"

    # ADCs used as benchmarks, controls, or approved comparators
    REFERENCE = "Reference"

class AntibodyDrugConjugate(BaseModel):
    """
    Comprehensive model for Antibody Drug Conjugate (ADC) information extraction.

    This model captures detailed information about ADCs including their components
    (antibody, linker, payload), targets, and supporting citations from research literature.

    An ADC consists of three main components:
    1. Antibody: Provides specificity for target antigen on tumour cells
    2. Linker: Chemical bridge connecting antibody to payload
    3. Payload: Cytotoxic drug that provides therapeutic effect by causing tumour cell death
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper containing the extracted information"
    )

    # ADC identification and classification
    adc_name: str = Field(
        ...,
        description="Official or common name of the Antibody Drug Conjugate. If the ADC does not have a specific name, then create one by combining the antibody name, linker name and payload name.",
    )

    adc_type: AntibodyDrugConjugateType = Field(
        ...,
        description="Classification of ADC role in the study: 'Investigative' for test subjects, 'Reference' for approved comparators or controls",
    )

    # Antibody component fields
    antibody_name: str = Field(
        ...,
        description="Name or identifier of the monoclonal antibody component",
    )

    antibody_clonality: AntibodyClonality = Field(
        ...,
        description="Clonality classification indicating whether derived from single (monoclonal) or multiple (polyclonal) B cell clones",
    )

    antibody_species: AntibodySpecies = Field(
        ...,
        description="Species origin and humanization status affecting immunogenicity profile",
    )

    antibody_isotype: AntibodyIsotype = Field(
        ...,
        description="Heavy chain class determining antibody structure and biological functions",
    )

    # Payload component fields
    payload_name: str = Field(
        ...,
        description="Chemical name or identifier of the cytotoxic drug payload",
    )

    payload_target: str = Field(
        ...,
        description="Specific molecular target, pathway, or mechanism of action for the cytotoxic payload",
    )

    # Linker component fields
    linker_name: str = Field(
        ...,
        description="Chemical name or identifier of the linker molecule connecting antibody to payload",
    )

    linker_type: LinkerType = Field(
        ...,
        description="Cleavage mechanism classification determining payload release strategy inside target cells",
    )

    # Target antigen fields
    antigen_name: str = Field(
        ...,
        description="Name or identifier of the cell surface antigen specifically recognized by the antibody component",
    )

print("="*100)
print(json.dumps(AntibodyDrugConjugate.model_json_schema(), indent=2))
print("="*100)

# ---- Definition of Preclinical Experimental Models ----


class ExperimentType(str, Enum):
    """
    Primary classification of experimental contexts for ADC research.

    This enum defines the fundamental experimental environments where ADC studies
    are conducted, determining the biological complexity and clinical relevance
    of the experimental system.
    
    IN_VITRO: Laboratory-based studies using cell cultures or biochemical systems
    IN_VIVO: Whole organism studies using animal models
    EX_VIVO: Tissue-based studies outside the living organism
    """
    # Laboratory-based studies using cell cultures or biochemical systems
    IN_VITRO = "In Vitro"

    # Whole organism studies using animal models
    IN_VIVO = "In Vivo"

    # Tissue-based studies outside the living organism
    EX_VIVO = "Ex Vivo"


class HostOrganismType(str, Enum):
    """
    Classification of host organisms used in experimental models.

    This enum categorizes the immune status and species of host organisms,
    which critically affects experimental outcomes and clinical translation.
    """
    # Mice lacking functional immune systems (nude, SCID, NSG, etc.)
    IMMUNOCOMPROMISED = "Immunocompromised"

    # Mice with intact, functional immune systems
    IMMUNOCOMPETENT = "Immunocompetent"

    # Non-human primates (monkeys, apes)
    PRIMATE = "Primate"

    # Not applicable for in vitro or ex vivo studies
    NOT_APPLICABLE = "Not Applicable"


class SourceMaterialType(str, Enum):
    """
    Classification of source material used to establish experimental models.

    This enum categorizes the origin of biological material, which affects
    model characteristics, genetic stability, and clinical relevance.
    """
    # Immortalized cancer cell lines maintained in culture
    CELL_LINE = "Cell Line"

    # Fresh tumor tissue directly from patients
    PRIMARY_PATIENT_TISSUE = "Primary Patient Tissue"

    # Genetically engineered mouse cancer cells
    MOUSE_CANCER_CELLS = "Mouse Cancer Cells"

    # Purified proteins, enzymes, or synthetic materials
    PURIFIED_COMPONENTS = "Purified Components"

    # Patient-derived organoids or tissue cultures
    PATIENT_DERIVED_CULTURES = "Patient-Derived Cultures"

    # Not applicable for certain model types
    NOT_APPLICABLE = "Not Applicable"



class PreclinicalExperimentalModel(BaseModel):
    """
    Comprehensive model for preclinical experimental systems used in ADC research and testing.

    This model captures detailed information about the experimental platforms used to evaluate
    Antibody Drug Conjugates (ADCs) in preclinical studies. It encompasses the full spectrum
    of model systems from simple cell culture to complex animal models, providing context
    for interpreting experimental results and translating findings to clinical applications.

    The model enforces scientific accuracy through structured attributes and validation rules
    that ensure logical consistency between experimental context, model classification,
    host organism characteristics, and source material types.

    Key Features:
    - Primary classification by experimental context (IN_VITRO, IN_VIVO, EX_VIVO)
    - Flexible model classification without enum constraints
    - Comprehensive validation of attribute relationships
    - Enhanced characterization of experimental conditions
    - Scientific accuracy through cross-field validation

    This information is critical for understanding the relevance and limitations of
    preclinical ADC efficacy and safety data.
    """

    # Citation and source information
    citations: List[str] = Field(
        ...,
        description="Direct quotes or sentences from the research paper describing the experimental model system used",
    )

    # Primary experimental classification
    experiment_type: ExperimentType = Field(
        ...,
        description="Primary classification of the experimental context determining biological complexity and clinical relevance",
    )

    # Model identification and classification
    model_name: str = Field(
        ...,
        description="Specific name or identifier of the experimental model system used in the ADC study",
    )

    model_classification: str = Field(
        ...,
        description="Specific model subtype classification (e.g., 'CDX', 'PDX', 'Cell Line', 'Organoid', 'Syngeneic', 'Transgenic', 'Non-cell based', 'Tissue Specimens', 'Rodent Models', 'Non-Human Primates')",
    )

    # Host organism and source material characteristics
    host_organism_type: HostOrganismType = Field(
        ...,
        description="Classification of the host organism's immune status and species, critical for interpreting experimental outcomes",
    )

    source_material_type: SourceMaterialType = Field(
        ...,
        description="Classification of the biological material source used to establish the experimental model",
    )

    # Enhanced experimental characterization
    culture_conditions: Optional[str] = Field(
        None,
        description="Detailed description of culture conditions, media, supplements, or environmental parameters for in vitro and ex vivo models",
    )

    implantation_site: Optional[str] = Field(
        None,
        description="Anatomical location of tumor implantation for in vivo models (e.g., 'subcutaneous', 'orthotopic', 'intravenous')",
    )

    genetic_modifications: Optional[str] = Field(
        None,
        description="Description of genetic modifications in transgenic models or engineered cell lines",
    )

    # Cancer classification fields
    cancer_type: str = Field(
        ...,
        description="Primary cancer classification based on the organ or tissue of origin where the malignancy first developed",
    )

    cancer_subtype: Optional[str] = Field(
        None,
        description="Detailed cancer classification based on histological, molecular, or genetic characteristics that influence treatment response and prognosis",
    )

    @field_validator('model_classification')
    @classmethod
    def validate_model_classification(cls, v: str) -> str:
        """Validate that model_classification uses standardized terminology."""
        valid_classifications = {
            # IN_VITRO models
            'Cell Line', 'Non-cell based',
            # EX_VIVO models
            'Organoid', 'Tissue Specimens',
            # IN_VIVO models
            'CDX', 'PDX', 'Syngeneic', 'Transgenic', 'Rodent Models', 'Non-Human Primates'
        }

        if v not in valid_classifications:
            raise ValueError(
                f"model_classification '{v}' must be one of: {', '.join(sorted(valid_classifications))}"
            )
        return v

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """Validate logical consistency between experiment_type and model_classification."""
        experiment_type = self.experiment_type
        model_classification = self.model_classification

        # Define valid combinations
        valid_combinations = {
            ExperimentType.IN_VITRO: {'Cell Line', 'Non-cell based'},
            ExperimentType.EX_VIVO: {'Organoid', 'Tissue Specimens'},
            ExperimentType.IN_VIVO: {'CDX', 'PDX', 'Syngeneic', 'Transgenic', 'Rodent Models', 'Non-Human Primates'}
        }

        if model_classification not in valid_combinations[experiment_type]:
            raise ValueError(
                f"Invalid combination: {experiment_type.value} experiments cannot use '{model_classification}' models. "
                f"Valid options for {experiment_type.value}: {', '.join(sorted(valid_combinations[experiment_type]))}"
            )

        return self

    @model_validator(mode='after')
    def validate_host_organism_consistency(self):
        """Validate logical consistency between experiment_type, model_classification, and host_organism_type."""
        experiment_type = self.experiment_type
        model_classification = self.model_classification
        host_organism_type = self.host_organism_type

        # IN_VITRO and EX_VIVO should use NOT_APPLICABLE
        if experiment_type in [ExperimentType.IN_VITRO, ExperimentType.EX_VIVO]:
            if host_organism_type != HostOrganismType.NOT_APPLICABLE:
                raise ValueError(
                    f"{experiment_type.value} experiments should use host_organism_type='Not Applicable', "
                    f"got '{host_organism_type.value}'"
                )

        # IN_VIVO model-specific validations
        elif experiment_type == ExperimentType.IN_VIVO:
            if host_organism_type == HostOrganismType.NOT_APPLICABLE:
                raise ValueError(
                    f"{experiment_type.value} experiments require a specific host_organism_type, "
                    f"cannot be 'Not Applicable'"
                )

            # CDX and PDX require immunocompromised hosts
            if model_classification in ['CDX', 'PDX']:
                if host_organism_type != HostOrganismType.IMMUNOCOMPROMISED:
                    raise ValueError(
                        f"'{model_classification}' models require immunocompromised hosts, "
                        f"got '{host_organism_type.value}'"
                    )

            # Syngeneic and Transgenic require immunocompetent hosts
            elif model_classification in ['Syngeneic', 'Transgenic']:
                if host_organism_type != HostOrganismType.IMMUNOCOMPETENT:
                    raise ValueError(
                        f"'{model_classification}' models require immunocompetent hosts, "
                        f"got '{host_organism_type.value}'"
                    )

            # Non-Human Primates require primate hosts
            elif model_classification == 'Non-Human Primates':
                if host_organism_type != HostOrganismType.PRIMATE:
                    raise ValueError(
                        f"'Non-Human Primates' models require primate hosts, "
                        f"got '{host_organism_type.value}'"
                    )

        return self

    @model_validator(mode='after')
    def validate_source_material_consistency(self):
        """Validate logical consistency between model_classification and source_material_type."""
        model_classification = self.model_classification
        source_material_type = self.source_material_type

        # Define expected source materials for each model type
        expected_source_materials = {
            'Cell Line': {SourceMaterialType.CELL_LINE},
            'Non-cell based': {SourceMaterialType.PURIFIED_COMPONENTS},
            'Organoid': {SourceMaterialType.PATIENT_DERIVED_CULTURES, SourceMaterialType.CELL_LINE},
            'Tissue Specimens': {SourceMaterialType.PRIMARY_PATIENT_TISSUE},
            'CDX': {SourceMaterialType.CELL_LINE},
            'PDX': {SourceMaterialType.PRIMARY_PATIENT_TISSUE},
            'Syngeneic': {SourceMaterialType.MOUSE_CANCER_CELLS},
            'Transgenic': {SourceMaterialType.NOT_APPLICABLE},  # Spontaneous development
            'Rodent Models': {SourceMaterialType.CELL_LINE, SourceMaterialType.MOUSE_CANCER_CELLS, SourceMaterialType.NOT_APPLICABLE},
            'Non-Human Primates': {SourceMaterialType.CELL_LINE, SourceMaterialType.PRIMARY_PATIENT_TISSUE, SourceMaterialType.NOT_APPLICABLE}
        }

        if model_classification in expected_source_materials:
            valid_sources = expected_source_materials[model_classification]
            if source_material_type not in valid_sources:
                valid_source_names = [source.value for source in valid_sources]
                raise ValueError(
                    f"Invalid source material for '{model_classification}' models: got '{source_material_type.value}'. "
                    f"Valid options: {', '.join(valid_source_names)}"
                )

        return self

    @model_validator(mode='after')
    def validate_field_applicability(self):
        """Validate that optional fields are used appropriately based on experiment type."""
        experiment_type = self.experiment_type

        # Culture conditions should only be specified for IN_VITRO and EX_VIVO
        if self.culture_conditions is not None:
            if experiment_type == ExperimentType.IN_VIVO:
                raise ValueError(
                    f"culture_conditions should not be specified for {experiment_type.value} experiments"
                )

        # Implantation site should only be specified for IN_VIVO
        if self.implantation_site is not None:
            if experiment_type != ExperimentType.IN_VIVO:
                raise ValueError(
                    f"implantation_site should only be specified for In Vivo experiments, "
                    f"not for {experiment_type.value}"
                )

        # Genetic modifications are most relevant for transgenic models and engineered cell lines
        if self.genetic_modifications is not None:
            valid_for_genetic_mods = ['Transgenic', 'Cell Line', 'Syngeneic']
            if self.model_classification not in valid_for_genetic_mods:
                # This is a warning rather than an error, as genetic modifications
                # could be relevant in other contexts
                pass

        return self
