# PreclinicalExperimentalModel Refactoring Documentation

## Overview

The `PreclinicalExperimentalModel` class has been comprehensively refactored to provide a more flexible, robust, and scientifically accurate data model for capturing experimental model characteristics in ADC research.

## Key Changes

### 1. Removed ModelType Enum Dependency
- **Before**: Used `model_type: ModelType` enum field
- **After**: Replaced with flexible `model_classification: str` field
- **Benefit**: Allows for more nuanced model descriptions while maintaining validation

### 2. Added ExperimentType as Primary Classification
- **New Field**: `experiment_type: ExperimentType` (required)
- **Values**: `IN_VITRO`, `IN_VIVO`, `EX_VIVO`
- **Purpose**: Primary classification determining experimental context and biological complexity

### 3. Enhanced Model Structure
- **New Fields**:
  - `experiment_type: ExperimentType` - Primary experimental context
  - `model_classification: str` - Specific model subtype (e.g., "CDX", "PDX", "Cell Line")
  - `host_organism_type: HostOrganismType` - Host immune status and species
  - `source_material_type: SourceMaterialType` - Origin of biological material
  - `culture_conditions: Optional[str]` - Culture parameters for in vitro/ex vivo
  - `implantation_site: Optional[str]` - Anatomical location for in vivo models
  - `genetic_modifications: Optional[str]` - Genetic engineering details

### 4. Comprehensive Validation Rules

#### Experiment-Model Consistency
- **IN_VITRO**: Only allows "Cell Line", "Non-cell based"
- **EX_VIVO**: Only allows "Organoid", "Tissue Specimens"  
- **IN_VIVO**: Only allows "CDX", "PDX", "Syngeneic", "Transgenic", "Rodent Models", "Non-Human Primates"

#### Host Organism Validation
- **IN_VITRO/EX_VIVO**: Must use `NOT_APPLICABLE`
- **CDX/PDX**: Require `IMMUNOCOMPROMISED` hosts
- **Syngeneic/Transgenic**: Require `IMMUNOCOMPETENT` hosts
- **Non-Human Primates**: Require `PRIMATE` hosts

#### Source Material Validation
- **Cell Line**: Must use `CELL_LINE` source
- **Non-cell based**: Must use `PURIFIED_COMPONENTS`
- **Organoid**: Allows `PATIENT_DERIVED_CULTURES` or `CELL_LINE`
- **Tissue Specimens**: Must use `PRIMARY_PATIENT_TISSUE`
- **CDX**: Must use `CELL_LINE`
- **PDX**: Must use `PRIMARY_PATIENT_TISSUE`
- **Syngeneic**: Must use `MOUSE_CANCER_CELLS`
- **Transgenic**: Uses `NOT_APPLICABLE` (spontaneous development)

#### Field Applicability
- **culture_conditions**: Only for IN_VITRO and EX_VIVO experiments
- **implantation_site**: Only for IN_VIVO experiments
- **genetic_modifications**: Most relevant for Transgenic, Cell Line, and Syngeneic models

## New Enums

### ExperimentType
```python
IN_VITRO = "In Vitro"      # Laboratory-based studies
IN_VIVO = "In Vivo"        # Whole organism studies  
EX_VIVO = "Ex Vivo"        # Tissue-based studies outside organism
```

### HostOrganismType
```python
IMMUNOCOMPROMISED = "Immunocompromised"  # Nude, SCID, NSG mice
IMMUNOCOMPETENT = "Immunocompetent"      # Normal immune system mice
PRIMATE = "Primate"                      # Non-human primates
NOT_APPLICABLE = "Not Applicable"        # For in vitro/ex vivo
```

### SourceMaterialType
```python
CELL_LINE = "Cell Line"                           # Immortalized cancer cells
PRIMARY_PATIENT_TISSUE = "Primary Patient Tissue" # Fresh patient tumor tissue
MOUSE_CANCER_CELLS = "Mouse Cancer Cells"         # Mouse-derived cancer cells
PURIFIED_COMPONENTS = "Purified Components"       # Proteins, enzymes, synthetic
PATIENT_DERIVED_CULTURES = "Patient-Derived Cultures" # Patient organoids/cultures
NOT_APPLICABLE = "Not Applicable"                 # For certain model types
```

## Benefits

1. **Scientific Accuracy**: Enforces real-world constraints through validation
2. **Flexibility**: String-based model classification allows nuanced descriptions
3. **Robustness**: Comprehensive cross-field validation prevents invalid combinations
4. **Clarity**: Clear hierarchical structure with primary experiment type classification
5. **Extensibility**: Easy to add new model types or validation rules

## Migration Notes

- Replace `model_type` references with `experiment_type` and `model_classification`
- Update validation logic to use new enum types
- Ensure all model instances specify the new required fields
- Review existing data for compliance with new validation rules
